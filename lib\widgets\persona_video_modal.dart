import 'package:flutter/material.dart';
import 'package:upshift/models/models.dart' as models;
import 'package:upshift/theme/theme.dart';
import 'package:upshift/widgets/persona_video_player.dart';

/// A modal dialog for displaying persona introduction videos
///
/// This widget provides:
/// - Full-screen modal presentation
/// - Video player with controls
/// - Persona information display
/// - Proper modal lifecycle management
/// - Responsive design
class PersonaVideoModal extends StatelessWidget {
  final models.SystemPersona persona;
  final String videoPath;

  const PersonaVideoModal({
    super.key,
    required this.persona,
    required this.videoPath,
  });

  /// Show the video modal in fullscreen
  static Future<void> show(
    BuildContext context, {
    required models.SystemPersona persona,
    String videoPath = 'assets/persona-videos/ZenMasterVideo.mp4',
  }) {
    return showGeneralDialog<void>(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.black,
      barrierLabel: 'Close video modal',
      pageBuilder: (context, animation, secondaryAnimation) =>
          PersonaVideoModal(persona: persona, videoPath: videoPath),
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(opacity: animation, child: child);
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // Fullscreen video player
            Center(
              child: AspectRatio(
                aspectRatio: 9 / 16, // 9:16 aspect ratio for 1080x1920 videos
                child: _buildVideoSection(context),
              ),
            ),

            // Close button overlay
            Positioned(
              top: AppDimensions.spacingM,
              right: AppDimensions.spacingM,
              child: _buildCloseButton(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoSection(BuildContext context) {
    return PersonaVideoPlayer(
      videoPath: videoPath,
      autoPlay: true,
      looping: false,
      showControls: true,
      aspectRatio: 9 / 16, // 9:16 aspect ratio for vertical videos
    );
  }

  Widget _buildCloseButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: const Icon(Icons.close, color: Colors.black87, size: 24),
        tooltip: 'Close video',
      ),
    );
  }
}
