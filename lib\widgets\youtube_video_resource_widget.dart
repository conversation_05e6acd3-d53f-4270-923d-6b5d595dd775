import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:youtube_player_iframe/youtube_player_iframe.dart';
import '../models/models.dart' as models;

/// A specialized widget for displaying YouTube video resources
/// with embedded video player functionality
class YouTubeVideoResourceWidget extends StatefulWidget {
  final models.ExternalResource resource;

  const YouTubeVideoResourceWidget({super.key, required this.resource});

  @override
  State<YouTubeVideoResourceWidget> createState() =>
      _YouTubeVideoResourceWidgetState();
}

class _YouTubeVideoResourceWidgetState
    extends State<YouTubeVideoResourceWidget> {
  YoutubePlayerController? _controller;
  bool _isPlayerReady = false;
  bool _hasError = false;
  String? _errorMessage;
  String? _videoId;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  void _initializePlayer() {
    try {
      _videoId = YoutubePlayerController.convertUrlToId(widget.resource.link);

      if (_videoId == null || _videoId!.isEmpty) {
        setState(() {
          _hasError = true;
          _errorMessage = 'Invalid YouTube URL';
        });
        return;
      }

      if (kDebugMode) {
        print('Initializing YouTube player with video ID: $_videoId');
        print('Original URL: ${widget.resource.link}');
      }

      // Initialize the controller with the video ID
      _controller = YoutubePlayerController.fromVideoId(
        videoId: _videoId!,
        autoPlay: false,
        params: const YoutubePlayerParams(
          mute: false,
          showControls: true,
          showFullscreenButton: true,
          loop: false,
          strictRelatedVideos: true,
        ),
      );

      // Set initial state
      setState(() {
        _hasError = false;
        _errorMessage = null;
        _isPlayerReady = true;
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing YouTube player: $e');
        print('Stack trace: ${StackTrace.current}');
      }
      setState(() {
        _hasError = true;
        _errorMessage = 'Error initializing video player: $e';
      });
    }
  }

  void _retryInitialization() {
    _initializePlayer();
  }

  @override
  void dispose() {
    _controller?.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and type badge
            Row(
              children: [
                Icon(
                  Icons.play_circle_outline,
                  size: 20,
                  color: colorScheme.primary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    widget.resource.title,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                // Resource type badge
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    widget.resource.type.displayName,
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: colorScheme.onPrimaryContainer,
                      fontSize: 10,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Video player or error state
            if (_hasError)
              _buildErrorState(context)
            else if (kIsWeb)
              _buildWebFallback(context)
            else if (_controller != null)
              _buildVideoPlayer(context)
            else
              _buildLoadingState(context),

            // Description
            if (widget.resource.description != null) ...[
              const SizedBox(height: 12),
              Text(
                widget.resource.description!,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],

            // Metadata (source and duration)
            if (widget.resource.source != null ||
                widget.resource.durationMinutes != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  if (widget.resource.source != null) ...[
                    Icon(
                      Icons.source,
                      size: 12,
                      color: colorScheme.onSurface.withValues(alpha: 0.5),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      widget.resource.source!,
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.5),
                        fontSize: 10,
                      ),
                    ),
                  ],
                  if (widget.resource.source != null &&
                      widget.resource.durationMinutes != null) ...[
                    const SizedBox(width: 12),
                    Text(
                      '•',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.5),
                        fontSize: 10,
                      ),
                    ),
                    const SizedBox(width: 12),
                  ],
                  if (widget.resource.durationMinutes != null) ...[
                    Icon(
                      Icons.schedule,
                      size: 12,
                      color: colorScheme.onSurface.withValues(alpha: 0.5),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${widget.resource.durationMinutes} min',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.5),
                        fontSize: 10,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildVideoPlayer(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: YoutubePlayer(controller: _controller!, aspectRatio: 16 / 9),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainer,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildErrorState(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: colorScheme.errorContainer.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colorScheme.error.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: colorScheme.error, size: 32),
            const SizedBox(height: 8),
            Text(
              _errorMessage ?? 'Failed to load video',
              style: theme.textTheme.bodySmall?.copyWith(
                color: colorScheme.error,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            TextButton.icon(
              onPressed: _retryInitialization,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWebFallback(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () => _openYouTubeInBrowser(),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.play_circle_outline,
                size: 48,
                color: colorScheme.primary,
              ),
              const SizedBox(height: 12),
              Text(
                'Watch on YouTube',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Tap to open video in a new tab',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Icon(
                Icons.open_in_new,
                size: 16,
                color: colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _openYouTubeInBrowser() async {
    try {
      final uri = Uri.parse(widget.resource.link);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Could not open ${widget.resource.link}')),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error opening YouTube URL: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error opening video: $e')));
      }
    }
  }
}
