import 'package:flutter/material.dart';
import 'package:upshift/models/models.dart' as models;
import 'package:upshift/theme/theme.dart';
import 'package:upshift/widgets/persona_video_modal.dart';

/// A reusable card widget for displaying SystemPersona information
///
/// This widget provides a vertical layout with:
/// - Centered persona avatar/image
/// - Persona name and description below the image
/// - Consistent theming and spacing
/// - Tap handling with visual feedback
/// - Support for selection states
/// - Optional video playback on avatar tap
class PersonaCard extends StatelessWidget {
  final models.SystemPersona persona;
  final bool isSelected;
  final VoidCallback? onTap;
  final VoidCallback? onAvatarTap;
  final bool showSelectionIndicator;
  final EdgeInsets? margin;
  final double? width;
  final double? height;

  const PersonaCard({
    super.key,
    required this.persona,
    this.isSelected = false,
    this.onTap,
    this.onAvatarTap,
    this.showSelectionIndicator = false,
    this.margin,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    // Calculate a taller card height to better utilize screen space
    final cardHeight =
        height ?? 520.0; // Increased from default to show more content

    return Container(
      width: width,
      height: cardHeight,
      margin: margin ?? const EdgeInsets.only(bottom: AppDimensions.spacingM),
      child: Card(
        elevation: AppDimensions.elevationS,
        child: InkWell(
          onTap: onTap,
          borderRadius: AppDimensions.borderRadiusM,
          child: Container(
            padding: const EdgeInsets.all(AppDimensions.spacingS),
            decoration: BoxDecoration(
              borderRadius: AppDimensions.borderRadiusM,
              border: isSelected
                  ? Border.all(color: context.colorScheme.primary, width: 2)
                  : null,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Avatar section - fixed height to ensure proper 2:3 aspect ratio
                SizedBox(
                  height: 380.0, // Fixed height to ensure proper aspect ratio
                  child: _buildAvatar(context),
                ),
                SizedBox(height: AppDimensions.spacingM),

                // Name with coaching style and approach tags
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.spacingS,
                  ),
                  child: _buildNameWithTags(context),
                ),

                SizedBox(height: AppDimensions.spacingS),

                // Metadata section - expanded to use remaining space
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.spacingS,
                    ),
                    child: _buildMetadata(context),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar(BuildContext context) {
    return GestureDetector(
      onTap: onAvatarTap ?? () => _showVideoModal(context),
      child: AspectRatio(
        aspectRatio: 2 / 3, // 2:3 aspect ratio for 1024x1536 images
        child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: AppDimensions.borderRadiusL,
            boxShadow: [
              BoxShadow(
                color: context.colorScheme.shadow.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: AppDimensions.borderRadiusL,
                  color: context.colorScheme.primaryContainer,
                  image: _getAvatarImage() != null
                      ? DecorationImage(
                          image: _getAvatarImage()!,
                          fit: BoxFit.cover,
                        )
                      : null,
                ),
                child: _getAvatarImage() == null
                    ? Icon(
                        AppIcons.profile,
                        size: 60,
                        color: context.colorScheme.onPrimaryContainer,
                      )
                    : null,
              ),
              // Play button overlay
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: AppDimensions.borderRadiusL,
                    color: Colors.black.withValues(alpha: 0.3),
                  ),
                  child: Icon(
                    Icons.play_circle_outline,
                    size: 40,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showVideoModal(BuildContext context) {
    PersonaVideoModal.show(
      context,
      persona: persona,
      videoPath: 'assets/persona-videos/ZenMasterVideo.mp4',
    );
  }

  Widget _buildNameWithTags(BuildContext context) {
    final metadata = persona.metadata;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Name with selection indicator
        Row(
          children: [
            Expanded(
              child: Text(
                persona.name,
                style: context.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (showSelectionIndicator && isSelected) ...[
              SizedBox(width: AppDimensions.spacingS),
              Icon(
                Icons.check_circle,
                color: context.colorScheme.primary,
                size: 20,
              ),
            ],
          ],
        ),

        // Coaching style and approach tags on the same line
        if (metadata != null &&
            (metadata['coachingStyle'] != null ||
                metadata['approach'] != null)) ...[
          SizedBox(height: AppDimensions.spacingS),
          Wrap(
            spacing: 6,
            runSpacing: 4,
            children: [
              if (metadata['coachingStyle'] != null)
                _buildCoachingStyleTag(
                  context,
                  metadata['coachingStyle'] as String,
                ),
              if (metadata['approach'] != null)
                _buildApproachTag(context, metadata['approach'] as String),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildMetadata(BuildContext context) {
    final metadata = persona.metadata;
    if (metadata == null) {
      return const SizedBox.shrink();
    }

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Specialties as tags
          if (metadata['specialties'] != null) ...[
            _buildSpecialtiesTags(context, metadata['specialties'] as List),
          ],
        ],
      ),
    );
  }

  Widget _buildCoachingStyleTag(BuildContext context, String coachingStyle) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: context.colorScheme.tertiary.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: context.colorScheme.tertiary.withValues(alpha: 0.4),
          width: 0.5,
        ),
      ),
      child: Text(
        coachingStyle.replaceAll('-', ' ').toLowerCase(),
        style: context.textTheme.bodySmall?.copyWith(
          color: context.colorScheme.tertiary,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildApproachTag(BuildContext context, String approach) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: context.colorScheme.tertiary.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: context.colorScheme.tertiary.withValues(alpha: 0.4),
          width: 0.5,
        ),
      ),
      child: Text(
        approach.replaceAll('-', ' ').toLowerCase(),
        style: context.textTheme.bodySmall?.copyWith(
          color: context.colorScheme.tertiary,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildSpecialtiesTags(BuildContext context, List specialties) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Text(
        //   'Specialties:',
        //   style: context.textTheme.bodySmall?.copyWith(
        //     color: context.colorScheme.onSurface.withValues(alpha: 0.6),
        //     fontWeight: FontWeight.w500,
        //     fontSize: 11,
        //   ),
        // ),
        // SizedBox(height: 4),
        Wrap(
          spacing: 4,
          runSpacing: 4,
          children: specialties.take(3).map<Widget>((specialty) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 7, vertical: 3),
              decoration: BoxDecoration(
                color: Colors.yellow.shade300,
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: Colors.yellow.shade600, width: 0.5),
              ),
              child: Text(
                (specialty as String).replaceAll('-', ' ').toLowerCase(),
                style: context.textTheme.bodySmall?.copyWith(
                  color: Colors
                      .black87, // Dark text for better visibility on yellow background
                  fontSize: 9.5,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  ImageProvider? _getAvatarImage() {
    if (persona.avatarUrl == null || persona.avatarUrl!.isEmpty) {
      return null;
    }

    if (persona.avatarUrl!.startsWith('assets/')) {
      return AssetImage(persona.avatarUrl!);
    } else {
      return NetworkImage(persona.avatarUrl!);
    }
  }
}
