// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserSubscription _$UserSubscriptionFromJson(Map<String, dynamic> json) =>
    UserSubscription(
      isActive: json['isActive'] as bool? ?? false,
      entitlements:
          (json['entitlements'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$SubscriptionEntitlementEnumMap, e))
              .toList() ??
          const [],
      subscriptionType: json['subscriptionType'] as String?,
      expirationDate: json['expirationDate'] == null
          ? null
          : DateTime.parse(json['expirationDate'] as String),
      purchaseDate: json['purchaseDate'] == null
          ? null
          : DateTime.parse(json['purchaseDate'] as String),
      originalTransactionId: json['originalTransactionId'] as String?,
      willRenew: json['willRenew'] as bool? ?? false,
      lastChecked: const TimestampDateTimeConverter().fromJson(
        json['lastChecked'],
      ),
    );

Map<String, dynamic> _$UserSubscriptionToJson(UserSubscription instance) =>
    <String, dynamic>{
      'isActive': instance.isActive,
      'entitlements': instance.entitlements
          .map((e) => _$SubscriptionEntitlementEnumMap[e]!)
          .toList(),
      'subscriptionType': instance.subscriptionType,
      'expirationDate': instance.expirationDate?.toIso8601String(),
      'purchaseDate': instance.purchaseDate?.toIso8601String(),
      'originalTransactionId': instance.originalTransactionId,
      'willRenew': instance.willRenew,
      'lastChecked': const TimestampDateTimeConverter().toJson(
        instance.lastChecked,
      ),
    };

const _$SubscriptionEntitlementEnumMap = {
  SubscriptionEntitlement.premium: 'premium',
};

NotificationPreferences _$NotificationPreferencesFromJson(
  Map<String, dynamic> json,
) => NotificationPreferences(
  enablePushNotifications: json['enablePushNotifications'] as bool? ?? true,
  enableEmailNotifications: json['enableEmailNotifications'] as bool? ?? true,
  topicPreferences:
      (json['topicPreferences'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as bool),
      ) ??
      const {},
  fcmTokens:
      (json['fcmTokens'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String),
      ) ??
      const {},
);

Map<String, dynamic> _$NotificationPreferencesToJson(
  NotificationPreferences instance,
) => <String, dynamic>{
  'enablePushNotifications': instance.enablePushNotifications,
  'enableEmailNotifications': instance.enableEmailNotifications,
  'topicPreferences': instance.topicPreferences,
  'fcmTokens': instance.fcmTokens,
};

User _$UserFromJson(Map<String, dynamic> json) => User(
  id: json['id'] as String,
  name: json['name'] as String,
  email: json['email'] as String,
  isOnboarded: json['isOnboarded'] as bool? ?? false,
  isAdmin: json['isAdmin'] as bool? ?? false,
  description: json['description'] as String?,
  preferredPersonaIds:
      (json['preferredPersonaIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  notificationPreferences: json['notificationPreferences'] == null
      ? null
      : NotificationPreferences.fromJson(
          json['notificationPreferences'] as Map<String, dynamic>,
        ),
  createdAt: const TimestampDateTimeConverter().fromJson(json['createdAt']),
);

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'email': instance.email,
  'isOnboarded': instance.isOnboarded,
  'isAdmin': instance.isAdmin,
  'description': instance.description,
  'preferredPersonaIds': instance.preferredPersonaIds,
  'notificationPreferences': instance.notificationPreferences,
  'createdAt': const TimestampDateTimeConverter().toJson(instance.createdAt),
};

Chat _$ChatFromJson(Map<String, dynamic> json) => Chat(
  id: json['id'] as String?,
  title: json['title'] as String,
  ownerId: json['ownerId'] as String,
  systemPersonaId: json['systemPersonaId'] as String,
  startedDate: const TimestampDateTimeConverter().fromJson(json['startedDate']),
  lastUpdatedDate: const TimestampDateTimeConverter().fromJson(
    json['lastUpdatedDate'],
  ),
  isCompleted: json['isCompleted'] as bool,
  archived: json['archived'] as bool?,
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$ChatToJson(Chat instance) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'ownerId': instance.ownerId,
  'systemPersonaId': instance.systemPersonaId,
  'startedDate': const TimestampDateTimeConverter().toJson(
    instance.startedDate,
  ),
  'lastUpdatedDate': const TimestampDateTimeConverter().toJson(
    instance.lastUpdatedDate,
  ),
  'isCompleted': instance.isCompleted,
  'archived': instance.archived,
  'metadata': instance.metadata,
};

Message _$MessageFromJson(Map<String, dynamic> json) => Message(
  id: json['id'] as String?,
  chatId: json['chatId'] as String,
  chatOwnerId: json['chatOwnerId'] as String,
  userId: json['userId'] as String?,
  systemPersonaId: json['systemPersonaId'] as String?,
  postedDate: const TimestampDateTimeConverter().fromJson(json['postedDate']),
  isDeleted: json['isDeleted'] as bool,
  type: json['type'] as String,
  textContent: json['textContent'] as String?,
  imageUrl: json['imageUrl'] as String?,
  reactionCounts: Map<String, int>.from(json['reactionCounts'] as Map),
  edited: json['edited'] as bool?,
  editDate: const NullableTimestampDateTimeConverter().fromJson(
    json['editDate'],
  ),
  replyToMessageId: json['replyToMessageId'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$MessageToJson(Message instance) => <String, dynamic>{
  'id': instance.id,
  'chatId': instance.chatId,
  'chatOwnerId': instance.chatOwnerId,
  'userId': instance.userId,
  'systemPersonaId': instance.systemPersonaId,
  'postedDate': const TimestampDateTimeConverter().toJson(instance.postedDate),
  'isDeleted': instance.isDeleted,
  'type': instance.type,
  'textContent': instance.textContent,
  'imageUrl': instance.imageUrl,
  'reactionCounts': instance.reactionCounts,
  'edited': instance.edited,
  'editDate': const NullableTimestampDateTimeConverter().toJson(
    instance.editDate,
  ),
  'replyToMessageId': instance.replyToMessageId,
  'metadata': instance.metadata,
};

SystemPersona _$SystemPersonaFromJson(Map<String, dynamic> json) =>
    SystemPersona(
      id: json['id'] as String?,
      name: json['name'] as String,
      avatarUrl: json['avatarUrl'] as String?,
      description: json['description'] as String?,
      isActive: json['isActive'] as bool?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$SystemPersonaToJson(SystemPersona instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'avatarUrl': instance.avatarUrl,
      'description': instance.description,
      'isActive': instance.isActive,
      'metadata': instance.metadata,
    };

RelationInfo _$RelationInfoFromJson(Map<String, dynamic> json) => RelationInfo(
  name: json['name'] as String,
  age: (json['age'] as num).toInt(),
  relation: json['relation'] as String,
  otherInfo: (json['otherInfo'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
);

Map<String, dynamic> _$RelationInfoToJson(RelationInfo instance) =>
    <String, dynamic>{
      'name': instance.name,
      'age': instance.age,
      'relation': instance.relation,
      'otherInfo': instance.otherInfo,
    };

Location _$LocationFromJson(Map<String, dynamic> json) =>
    Location(town: json['town'] as String?, country: json['country'] as String);

Map<String, dynamic> _$LocationToJson(Location instance) => <String, dynamic>{
  'town': instance.town,
  'country': instance.country,
};

Fact _$FactFromJson(Map<String, dynamic> json) =>
    Fact(key: json['key'] as String, value: json['value']);

Map<String, dynamic> _$FactToJson(Fact instance) => <String, dynamic>{
  'key': instance.key,
  'value': instance.value,
};

Goal _$GoalFromJson(Map<String, dynamic> json) => Goal(
  id: json['id'] as String,
  description: json['description'] as String,
  status: json['status'] as String,
  createdAt: const TimestampDateTimeConverter().fromJson(json['createdAt']),
  updatedAt: const NullableTimestampDateTimeConverter().fromJson(
    json['updatedAt'],
  ),
);

Map<String, dynamic> _$GoalToJson(Goal instance) => <String, dynamic>{
  'id': instance.id,
  'description': instance.description,
  'status': instance.status,
  'createdAt': const TimestampDateTimeConverter().toJson(instance.createdAt),
  'updatedAt': const NullableTimestampDateTimeConverter().toJson(
    instance.updatedAt,
  ),
};

InteractionSource _$InteractionSourceFromJson(Map<String, dynamic> json) =>
    InteractionSource(
      sessionId: json['sessionId'] as String,
      timestamp: const TimestampDateTimeConverter().fromJson(json['timestamp']),
    );

Map<String, dynamic> _$InteractionSourceToJson(
  InteractionSource instance,
) => <String, dynamic>{
  'sessionId': instance.sessionId,
  'timestamp': const TimestampDateTimeConverter().toJson(instance.timestamp),
};

UserProfile _$UserProfileFromJson(Map<String, dynamic> json) => UserProfile(
  userId: json['userId'] as String,
  name: json['name'] as String?,
  age: (json['age'] as num?)?.toInt(),
  gender: json['gender'] as String?,
  familyStatus: json['familyStatus'] as String?,
  family: (json['family'] as List<dynamic>?)
      ?.map((e) => RelationInfo.fromJson(e as Map<String, dynamic>))
      .toList(),
  location: json['location'] == null
      ? null
      : Location.fromJson(json['location'] as Map<String, dynamic>),
  facts: (json['facts'] as List<dynamic>?)
      ?.map((e) => Fact.fromJson(e as Map<String, dynamic>))
      .toList(),
  likes: (json['likes'] as List<dynamic>?)?.map((e) => e as String).toList(),
  dislikes: (json['dislikes'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  preferences: json['preferences'] as Map<String, dynamic>?,
  goals: (json['goals'] as List<dynamic>?)
      ?.map((e) => Goal.fromJson(e as Map<String, dynamic>))
      .toList(),
  personalityTraits: (json['personalityTraits'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  interactionHistory: InteractionHistory.fromJson(
    json['interactionHistory'] as Map<String, dynamic>,
  ),
);

Map<String, dynamic> _$UserProfileToJson(UserProfile instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'name': instance.name,
      'age': instance.age,
      'gender': instance.gender,
      'familyStatus': instance.familyStatus,
      'family': instance.family?.map((e) => e.toJson()).toList(),
      'location': instance.location?.toJson(),
      'facts': instance.facts?.map((e) => e.toJson()).toList(),
      'likes': instance.likes,
      'dislikes': instance.dislikes,
      'preferences': instance.preferences,
      'goals': instance.goals?.map((e) => e.toJson()).toList(),
      'personalityTraits': instance.personalityTraits,
      'interactionHistory': instance.interactionHistory.toJson(),
    };

InteractionHistory _$InteractionHistoryFromJson(Map<String, dynamic> json) =>
    InteractionHistory(
      lastUpdated: const TimestampDateTimeConverter().fromJson(
        json['lastUpdated'],
      ),
      sources: (json['sources'] as List<dynamic>?)
          ?.map((e) => InteractionSource.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$InteractionHistoryToJson(InteractionHistory instance) =>
    <String, dynamic>{
      'lastUpdated': const TimestampDateTimeConverter().toJson(
        instance.lastUpdated,
      ),
      'sources': instance.sources?.map((e) => e.toJson()).toList(),
    };

GuidedPath _$GuidedPathFromJson(Map<String, dynamic> json) => GuidedPath(
  id: json['id'] as String?,
  name: json['name'] as String,
  category: json['category'] as String,
  description: json['description'] as String,
  stepCount: (json['stepCount'] as num).toInt(),
  targetUserTier: json['targetUserTier'] as String,
  imageUrl: json['imageUrl'] as String?,
  estimatedCompletionTimeMinutes:
      (json['estimatedCompletionTimeMinutes'] as num?)?.toInt(),
  difficultyLevel: json['difficultyLevel'] as String?,
  prerequisites: (json['prerequisites'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  isActive: json['isActive'] as bool? ?? true,
  createdAt: const TimestampDateTimeConverter().fromJson(json['createdAt']),
  updatedAt: const NullableTimestampDateTimeConverter().fromJson(
    json['updatedAt'],
  ),
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$GuidedPathToJson(
  GuidedPath instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'category': instance.category,
  'description': instance.description,
  'stepCount': instance.stepCount,
  'targetUserTier': instance.targetUserTier,
  'imageUrl': instance.imageUrl,
  'estimatedCompletionTimeMinutes': instance.estimatedCompletionTimeMinutes,
  'difficultyLevel': instance.difficultyLevel,
  'prerequisites': instance.prerequisites,
  'isActive': instance.isActive,
  'createdAt': const TimestampDateTimeConverter().toJson(instance.createdAt),
  'updatedAt': const NullableTimestampDateTimeConverter().toJson(
    instance.updatedAt,
  ),
  'metadata': instance.metadata,
};

ExternalResource _$ExternalResourceFromJson(Map<String, dynamic> json) =>
    ExternalResource(
      title: json['title'] as String,
      link: json['link'] as String,
      type: $enumDecode(_$ExternalResourceTypeEnumMap, json['type']),
      description: json['description'] as String?,
      source: json['source'] as String?,
      durationMinutes: (json['durationMinutes'] as num?)?.toInt(),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$ExternalResourceToJson(ExternalResource instance) =>
    <String, dynamic>{
      'title': instance.title,
      'link': instance.link,
      'type': _$ExternalResourceTypeEnumMap[instance.type]!,
      'description': instance.description,
      'source': instance.source,
      'durationMinutes': instance.durationMinutes,
      'metadata': instance.metadata,
    };

const _$ExternalResourceTypeEnumMap = {
  ExternalResourceType.article: 'article',
  ExternalResourceType.video: 'video',
  ExternalResourceType.podcast: 'podcast',
  ExternalResourceType.tool: 'tool',
  ExternalResourceType.book: 'book',
  ExternalResourceType.course: 'course',
  ExternalResourceType.website: 'website',
  ExternalResourceType.other: 'other',
};

PathStep _$PathStepFromJson(Map<String, dynamic> json) => PathStep(
  id: json['id'] as String?,
  pathId: json['pathId'] as String,
  stepNumber: (json['stepNumber'] as num).toInt(),
  title: json['title'] as String,
  description: json['description'] as String,
  completionCriteria: json['completionCriteria'] as String,
  estimatedDurationMinutes: (json['estimatedDurationMinutes'] as num?)?.toInt(),
  resources: (json['resources'] as List<dynamic>?)
      ?.map((e) => ExternalResource.fromJson(e as Map<String, dynamic>))
      .toList(),
  reflectionPrompts: (json['reflectionPrompts'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  isActive: json['isActive'] as bool? ?? true,
  createdAt: const TimestampDateTimeConverter().fromJson(json['createdAt']),
  updatedAt: const NullableTimestampDateTimeConverter().fromJson(
    json['updatedAt'],
  ),
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$PathStepToJson(PathStep instance) => <String, dynamic>{
  'id': instance.id,
  'pathId': instance.pathId,
  'stepNumber': instance.stepNumber,
  'title': instance.title,
  'description': instance.description,
  'completionCriteria': instance.completionCriteria,
  'estimatedDurationMinutes': instance.estimatedDurationMinutes,
  'resources': instance.resources?.map((e) => e.toJson()).toList(),
  'reflectionPrompts': instance.reflectionPrompts,
  'isActive': instance.isActive,
  'createdAt': const TimestampDateTimeConverter().toJson(instance.createdAt),
  'updatedAt': const NullableTimestampDateTimeConverter().toJson(
    instance.updatedAt,
  ),
  'metadata': instance.metadata,
};

UserPathProgress _$UserPathProgressFromJson(Map<String, dynamic> json) =>
    UserPathProgress(
      id: json['id'] as String?,
      userId: json['userId'] as String,
      pathId: json['pathId'] as String,
      currentStepNumber: (json['currentStepNumber'] as num?)?.toInt() ?? 1,
      completedSteps:
          (json['completedSteps'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [],
      status: json['status'] as String? ?? 'not_started',
      startedDate: const TimestampDateTimeConverter().fromJson(
        json['startedDate'],
      ),
      lastAccessedDate: const TimestampDateTimeConverter().fromJson(
        json['lastAccessedDate'],
      ),
      completionDate: const NullableTimestampDateTimeConverter().fromJson(
        json['completionDate'],
      ),
      progressMetadata: json['progressMetadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$UserPathProgressToJson(UserPathProgress instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'pathId': instance.pathId,
      'currentStepNumber': instance.currentStepNumber,
      'completedSteps': instance.completedSteps,
      'status': instance.status,
      'startedDate': const TimestampDateTimeConverter().toJson(
        instance.startedDate,
      ),
      'lastAccessedDate': const TimestampDateTimeConverter().toJson(
        instance.lastAccessedDate,
      ),
      'completionDate': const NullableTimestampDateTimeConverter().toJson(
        instance.completionDate,
      ),
      'progressMetadata': instance.progressMetadata,
    };
